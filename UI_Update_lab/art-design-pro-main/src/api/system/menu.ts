import { http } from '@/utils/http'
import type { Menu, Menu<PERSON>uery, MenuForm } from '@/types/system/menu'
import type { RuoyiResponse } from '@/types/global'

/**
 * 查询菜单列表
 */
export function listMenu(query?: MenuQuery): Promise<RuoyiResponse<Menu[]>> {
  return http.get('/system/menu/list', { params: query })
}

/**
 * 查询菜单详细
 */
export function getMenu(menuId: number): Promise<RuoyiResponse<Menu>> {
  return http.get(`/system/menu/${menuId}`)
}

/**
 * 新增菜单
 */
export function addMenu(data: MenuForm): Promise<RuoyiResponse<void>> {
  return http.post('/system/menu', data)
}

/**
 * 修改菜单
 */
export function updateMenu(data: MenuForm): Promise<RuoyiResponse<void>> {
  return http.put('/system/menu', data)
}

/**
 * 删除菜单
 */
export function delMenu(menuId: number): Promise<RuoyiResponse<void>> {
  return http.delete(`/system/menu/${menuId}`)
}

/**
 * 查询菜单下拉树结构
 */
export function treeselect(): Promise<RuoyiResponse<Menu[]>> {
  return http.get('/system/menu/treeselect')
}

/**
 * 根据角色ID查询菜单下拉树结构
 */
export function roleMenuTreeselect(roleId: number): Promise<RuoyiResponse<{
  checkedKeys: number[]
  menus: Menu[]
}>> {
  return http.get(`/system/menu/roleMenuTreeselect/${roleId}`)
}
