let icons = []

// 尝试从多个可能的路径加载图标
const possiblePaths = [
  './../../assets/icons/svg/*.svg',
  './../../../assets/icons/svg/*.svg',
  './../../assets/icons/*.svg'
]

// 尝试加载图标文件
for (const path of possiblePaths) {
  try {
    const modules = import.meta.glob(path)
    for (const modulePath in modules) {
      const pathParts = modulePath.split('/')
      const fileName = pathParts[pathParts.length - 1]
      const iconName = fileName.replace('.svg', '')
      if (iconName && !icons.includes(iconName)) {
        icons.push(iconName)
      }
    }
    if (icons.length > 0) break
  } catch (e) {
    console.warn(`Failed to load icons from ${path}:`, e)
  }
}

// 如果没有找到图标文件，提供一些默认图标
if (icons.length === 0) {
  icons = [
    'user',
    'lock',
    'eye',
    'eye-open',
    'search',
    'edit',
    'delete',
    'add',
    'home',
    'setting',
    'menu',
    'close',
    'check',
    'arrow-left',
    'arrow-right',
    'arrow-up',
    'arrow-down',
    'star',
    'heart',
    'message',
    'phone',
    'email',
    'calendar',
    'clock',
    'location',
    'camera',
    'image',
    'file',
    'folder',
    'download',
    'upload'
  ]
}

export default icons
